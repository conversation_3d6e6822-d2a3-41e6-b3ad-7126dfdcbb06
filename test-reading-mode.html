<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Reading Mode Prevention</title>
    
    <!-- Disable Safari Reading Mode -->
    <meta name="reader-view" content="disabled">
    <meta name="safari-reader" content="disabled">
    <meta name="readability-score" content="0">
    <meta name="article" content="false">
    <meta name="document-type" content="website">
    <meta name="content-type" content="commercial-website">
    <meta name="application-name" content="X-ZoneServers">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #020617;
            color: #d1d5db;
            margin: 0;
            padding: 20px;
            -webkit-reader-mode: disabled;
            -webkit-text-size-adjust: 100%;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            -webkit-reader-mode: disabled;
        }
        
        .btn {
            background: linear-gradient(45deg, #0ea5e9, #8b5cf6);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            -webkit-reader-mode: disabled;
        }
        
        .interactive-section {
            background: rgba(14, 165, 233, 0.1);
            border: 1px solid rgba(14, 165, 233, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
    
    <script>
        // Prevent Safari Reading Mode
        (function() {
            document.documentElement.setAttribute('data-reader', 'false');
            document.documentElement.setAttribute('data-article', 'false');
            
            const preventReadingMode = () => {
                const elements = document.querySelectorAll('main, section, article, div');
                elements.forEach(el => {
                    el.setAttribute('data-reader', 'false');
                    el.setAttribute('role', 'presentation');
                });
                
                const interactive = document.querySelectorAll('button, a, input');
                interactive.forEach(el => {
                    el.setAttribute('data-reader', 'false');
                });
            };
            
            preventReadingMode();
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', preventReadingMode);
            }
        })();
    </script>
</head>
<body data-reader="false" data-article="false" itemscope itemtype="https://schema.org/WebPage">
    <div class="container" data-reader="false">
        <h1>Reading Mode Prevention Test</h1>
        
        <div class="interactive-section">
            <h2>Interactive Elements</h2>
            <p>This page tests whether Safari's reading mode is prevented.</p>
            
            <button class="btn" onclick="alert('Button clicked!')">Click Me</button>
            <button class="btn" onclick="document.body.style.background = '#1e293b'">Change Background</button>
            
            <div style="margin: 20px 0;">
                <input type="text" placeholder="Type something..." style="padding: 8px; margin-right: 10px;">
                <button class="btn" onclick="alert('Form interaction!')">Submit</button>
            </div>
        </div>
        
        <div class="interactive-section">
            <h2>Commercial Content</h2>
            <p>This is a commercial website for hosting services, not an article.</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                <div style="background: rgba(139, 92, 246, 0.1); padding: 15px; border-radius: 8px;">
                    <h3>VPS Hosting</h3>
                    <p>Starting at €9.50/month</p>
                    <button class="btn">Order Now</button>
                </div>
                <div style="background: rgba(236, 72, 153, 0.1); padding: 15px; border-radius: 8px;">
                    <h3>Dedicated Servers</h3>
                    <p>Starting at €209/month</p>
                    <button class="btn">Order Now</button>
                </div>
            </div>
        </div>
        
        <div class="interactive-section">
            <h2>Navigation Links</h2>
            <nav>
                <a href="#" style="color: #0ea5e9; margin-right: 20px;">Home</a>
                <a href="#" style="color: #0ea5e9; margin-right: 20px;">Services</a>
                <a href="#" style="color: #0ea5e9; margin-right: 20px;">Pricing</a>
                <a href="#" style="color: #0ea5e9; margin-right: 20px;">Contact</a>
            </nav>
        </div>
        
        <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.1);">
            <p>&copy; 2025 X-ZoneServers. All rights reserved.</p>
        </footer>
    </div>
</body>
</html>
