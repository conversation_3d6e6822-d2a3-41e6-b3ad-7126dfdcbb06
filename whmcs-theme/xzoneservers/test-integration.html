<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WHMCS Theme Integration Test - X-ZoneServers</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'slate': {
                            950: '#020617',
                            900: '#0f172a',
                            800: '#1e293b',
                            700: '#334155',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Lucide Icons -->
    <script defer src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <style>
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: #000;
            color: #fff;
            padding: 8px;
            text-decoration: none;
            z-index: 1000;
        }
        .skip-link:focus {
            top: 6px;
        }
        .bg-gradient-radial {
            background: radial-gradient(circle, var(--tw-gradient-stops));
        }
    </style>
</head>
<body class="antialiased bg-slate-950 text-gray-300">
    <!-- Accessibility skip links -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <a href="#footer" class="skip-link">Skip to footer</a>

    <!-- Enhanced Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-slate-950/95 via-slate-900/95 to-slate-950/95 backdrop-blur-xl border-b border-slate-800/50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo -->
                <a href="../index.html" class="text-2xl font-bold text-white flex items-center group">
                    <span class="bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                        X-Zone<span class="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">Servers</span>
                    </span>
                </a>

                <!-- Desktop Navigation -->
                <nav class="hidden xl:flex items-center space-x-1">
                    <div class="relative group">
                        <button class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link flex items-center" data-page="vps">
                            <span class="relative z-10 flex items-center">
                                <i data-lucide="server" class="w-4 h-4 mr-2"></i>
                                VPS
                                <i data-lucide="chevron-down" class="w-3 h-3 ml-1 transition-transform duration-300 group-hover:rotate-180"></i>
                            </span>
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </button>
                        <!-- VPS Dropdown -->
                        <div class="absolute top-full left-0 mt-2 w-48 bg-slate-900/95 backdrop-blur-xl border border-slate-700/50 rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-2">
         
                                <a href="../streaming-vps.html" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                                    <i data-lucide="video" class="w-4 h-4 mr-3"></i>
                                    Streaming VPS
                                </a>
                            </div>
                        </div>
                    </div>
                    <a href="../dedicated.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="dedicated">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="database" class="w-4 h-4 mr-2"></i>
                            Dedicated Servers
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <a href="../game-hosting.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="game-hosting">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="gamepad-2" class="w-4 h-4 mr-2"></i>
                            Game Hosting
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <a href="../network.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="network">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="network" class="w-4 h-4 mr-2"></i>
                            Network
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <a href="../about.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="about">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                            About Us
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-pink-500/10 to-orange-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <a href="../contact.html" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link" data-page="contact">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="phone" class="w-4 h-4 mr-2"></i>
                            Contact
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-teal-500/10 to-cyan-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                </nav>

                <!-- Login/Sign Up Buttons -->
                <div class="hidden xl:flex items-center space-x-4">
                    <a href="login.php" class="px-4 py-2 text-gray-300 hover:text-white transition-all duration-300">
                        <i data-lucide="log-in" class="w-4 h-4 mr-2 inline"></i>
                        Login
                    </a>
                    <a href="register.php" class="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105" id="header-cta">
                        <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                        Sign Up
                    </a>
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="xl:hidden p-2 text-white hover:bg-slate-800/50 rounded-lg transition-colors duration-300 relative">
                    <div class="w-6 h-6 flex flex-col justify-center items-center">
                        <span class="hamburger-line block w-5 h-0.5 bg-white transition-all duration-300 ease-in-out"></span>
                        <span class="hamburger-line block w-5 h-0.5 bg-white mt-1 transition-all duration-300 ease-in-out"></span>
                        <span class="hamburger-line block w-5 h-0.5 bg-white mt-1 transition-all duration-300 ease-in-out"></span>
                    </div>
                </button>
            </div>
        </div>

        <!-- Enhanced Mobile Menu -->
        <div id="mobile-menu" class="hidden xl:hidden bg-gradient-to-br from-slate-950/98 to-slate-900/98 backdrop-blur-xl border-t border-slate-800/50">
            <div class="px-4 py-6 space-y-3">
                <div class="vps-mobile-dropdown">
                    <button class="flex items-center justify-between w-full px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="vps" id="mobile-vps-toggle">
                        <div class="flex items-center">
                            <i data-lucide="server" class="w-5 h-5 mr-3"></i>
                            VPS
                        </div>
                        <i data-lucide="chevron-down" class="w-4 h-4 transition-transform duration-300" id="mobile-vps-chevron"></i>
                    </button>
                    <div class="mobile-vps-submenu hidden mt-2 ml-4 space-y-2">
                        <a href="../shared-vps.html" class="flex items-center px-4 py-2 text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="share-2" class="w-4 h-4 mr-3"></i>
                            Shared VPS
                        </a>
                        <a href="../streaming-vps.html" class="flex items-center px-4 py-2 text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="video" class="w-4 h-4 mr-3"></i>
                            Streaming VPS
                        </a>
                    </div>
                </div>
                <a href="../dedicated.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="dedicated">
                    <i data-lucide="database" class="w-5 h-5 mr-3"></i>
                    Dedicated Servers
                </a>
                <a href="../game-hosting.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-red-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="game-hosting">
                    <i data-lucide="gamepad-2" class="w-5 h-5 mr-3"></i>
                    Game Hosting
                </a>
                <a href="../network.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-green-500/10 hover:to-blue-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="network">
                    <i data-lucide="network" class="w-5 h-5 mr-3"></i>
                    Network
                </a>
                <a href="../about.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-pink-500/10 hover:to-orange-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="about">
                    <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                    About Us
                </a>
                <a href="../contact.html" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-teal-500/10 hover:to-cyan-500/10 rounded-xl transition-all duration-300 mobile-nav-link" data-page="contact">
                    <i data-lucide="phone" class="w-5 h-5 mr-3"></i>
                    Contact
                </a>
                <div class="pt-4 border-t border-slate-800/50 space-y-3">
                    <a href="login.php" class="flex items-center justify-center w-full px-6 py-3 bg-slate-800/50 text-white font-semibold rounded-xl hover:bg-slate-700/50 transition-all duration-300">
                        <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                        Login
                    </a>
                    <a href="register.php" class="flex items-center justify-center w-full px-6 py-3 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300" id="mobile-header-cta">
                        <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                        Sign Up
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content Container -->
    <main id="main-content" role="main" class="pt-20">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="w-full py-16">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-white mb-8">WHMCS Theme Integration Test</h1>
                    <p class="text-xl text-gray-300 mb-8">Testing the synchronized header and footer between homepage and WHMCS</p>
                    
                    <div class="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-2xl p-8 max-w-4xl mx-auto">
                        <h2 class="text-2xl font-bold text-white mb-4">Integration Status</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-3">✅ Header Updates</h3>
                                <ul class="space-y-2 text-gray-300">
                                    <li>• X-ZoneServers logo matching homepage</li>
                                    <li>• VPS dropdown with Shared VPS and Streaming VPS</li>
                                    <li>• Dedicated Servers link</li>
                                    <li>• Game Hosting link</li>
                                    <li>• Network link</li>
                                    <li>• About Us link</li>
                                    <li>• Contact link</li>
                                    <li>• Login and Sign Up buttons (instead of Client Area)</li>
                                </ul>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-3">✅ Footer Updates</h3>
                                <ul class="space-y-2 text-gray-300">
                                    <li>• Services section with homepage links</li>
                                    <li>• Company section with About, Network, Use Cases</li>
                                    <li>• Support section with Knowledge Base, Tickets</li>
                                    <li>• Updated copyright and legal links</li>
                                    <li>• System status indicator</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
